-- 工作台菜单权限配置SQL脚本
-- 用于解决工作台页面权限控制问题

-- 1. 添加工作台主菜单（如果不存在）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '工作台', 0, 1, 'workbench', 'workbench/fixed', 1, 0, 'C', '0', '0', 'workbench:view', 'dashboard', 'admin', NOW(), '系统工作台菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = 'workbench' AND parent_id = 0);

-- 获取工作台菜单ID
SET @workbench_menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'workbench' AND parent_id = 0 LIMIT 1);

-- 2. 添加资产工作台相关权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '工作台查看', @workbench_menu_id, 1, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:view', '#', 'admin', NOW(), '查看工作台数据权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:view');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '资产概览', @workbench_menu_id, 2, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:overview', '#', 'admin', NOW(), '查看资产概览权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:overview');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '资产趋势', @workbench_menu_id, 3, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:trends', '#', 'admin', NOW(), '查看资产趋势权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:trends');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '入库出库统计', @workbench_menu_id, 4, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:inout', '#', 'admin', NOW(), '查看入库出库统计权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:inout');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '处置统计', @workbench_menu_id, 5, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:disposal', '#', 'admin', NOW(), '查看处置统计权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:disposal');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '状态分布', @workbench_menu_id, 6, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:status', '#', 'admin', NOW(), '查看资产状态分布权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:status');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '价值统计', @workbench_menu_id, 7, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:value', '#', 'admin', NOW(), '查看资产价值统计权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:value');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '待办事项', @workbench_menu_id, 8, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:todo', '#', 'admin', NOW(), '查看待办事项权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:todo');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '刷新数据', @workbench_menu_id, 9, '#', '', 1, 0, 'F', '0', '0', 'asset:workbench:refresh', '#', 'admin', NOW(), '刷新工作台数据权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'asset:workbench:refresh');

-- 3. 添加系统工作台相关权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '系统统计', @workbench_menu_id, 10, '#', '', 1, 0, 'F', '0', '0', 'system:workbench:statistics', '#', 'admin', NOW(), '查看系统统计权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:workbench:statistics');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
SELECT '用户活动', @workbench_menu_id, 11, '#', '', 1, 0, 'F', '0', '0', 'system:workbench:activity', '#', 'admin', NOW(), '查看用户活动统计权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:workbench:activity');

-- 4. 为管理员角色分配所有工作台权限
-- 获取管理员角色ID（通常是1）
SET @admin_role_id = 1;

-- 为管理员角色分配工作台主菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @admin_role_id, menu_id FROM sys_menu 
WHERE path = 'workbench' AND parent_id = 0
AND NOT EXISTS (SELECT 1 FROM sys_role_menu WHERE role_id = @admin_role_id AND menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'workbench' AND parent_id = 0 LIMIT 1));

-- 为管理员角色分配所有工作台相关权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @admin_role_id, menu_id FROM sys_menu 
WHERE parent_id = @workbench_menu_id
AND NOT EXISTS (SELECT 1 FROM sys_role_menu rm WHERE rm.role_id = @admin_role_id AND rm.menu_id = sys_menu.menu_id);

-- 5. 为普通用户角色分配基本工作台权限（角色ID为2，根据实际情况调整）
SET @user_role_id = 2;

-- 为普通用户角色分配工作台主菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @user_role_id, menu_id FROM sys_menu 
WHERE path = 'workbench' AND parent_id = 0
AND EXISTS (SELECT 1 FROM sys_role WHERE role_id = @user_role_id)
AND NOT EXISTS (SELECT 1 FROM sys_role_menu WHERE role_id = @user_role_id AND menu_id = (SELECT menu_id FROM sys_menu WHERE path = 'workbench' AND parent_id = 0 LIMIT 1));

-- 为普通用户角色分配基本查看权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @user_role_id, menu_id FROM sys_menu 
WHERE parent_id = @workbench_menu_id 
AND perms IN ('asset:workbench:view', 'asset:workbench:overview', 'asset:workbench:trends', 'asset:workbench:inout', 'asset:workbench:disposal', 'asset:workbench:status', 'asset:workbench:value', 'asset:workbench:todo', 'system:workbench:statistics', 'system:workbench:activity')
AND EXISTS (SELECT 1 FROM sys_role WHERE role_id = @user_role_id)
AND NOT EXISTS (SELECT 1 FROM sys_role_menu rm WHERE rm.role_id = @user_role_id AND rm.menu_id = sys_menu.menu_id);

-- 查询结果验证
SELECT '工作台菜单配置完成' as message;
SELECT menu_id, menu_name, perms, status FROM sys_menu WHERE path = 'workbench' OR parent_id = @workbench_menu_id ORDER BY parent_id, order_num;

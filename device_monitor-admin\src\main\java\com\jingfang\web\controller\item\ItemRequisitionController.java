package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.wh_item_requisition.module.dto.ItemRequisitionDto;
import com.jingfang.wh_item_requisition.module.request.ItemRequisitionSearchRequest;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionVo;
import com.jingfang.wh_item_requisition.service.ItemRequisitionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 物品领用单控制器
 */
@Slf4j
@RestController
@RequestMapping("/item/requisition")
public class ItemRequisitionController extends BaseController {

    @Resource
    private ItemRequisitionService itemRequisitionService;

    /**
     * 新增领用单
     *
     * @param requisitionDto 领用单信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ItemRequisitionDto requisitionDto) {
        try {
            log.info("新增领用单请求参数：{}", requisitionDto);
            String username = SecurityUtils.getUsername();
            String requisitionId = itemRequisitionService.addItemRequisition(requisitionDto, username);
            return AjaxResult.success("新增领用单成功", requisitionId);
        } catch (Exception e) {
            log.error("新增领用单异常：", e);
            return AjaxResult.error("新增领用单失败：" + e.getMessage());
        }
    }

    /**
     * 修改领用单
     *
     * @param requisitionId 领用单ID
     * @param requisitionDto 领用单信息
     * @return 操作结果
     */
    @PutMapping("/{requisitionId}")
    public AjaxResult edit(@PathVariable String requisitionId, @RequestBody ItemRequisitionDto requisitionDto) {
        try {
            log.info("修改领用单[{}]请求参数：{}", requisitionId, requisitionDto);
            String username = SecurityUtils.getUsername();
            itemRequisitionService.updateItemRequisition(requisitionId, requisitionDto, username);
            return AjaxResult.success("修改领用单成功");
        } catch (Exception e) {
            log.error("修改领用单异常：", e);
            return AjaxResult.error("修改领用单失败：" + e.getMessage());
        }
    }

    /**
     * 提交领用单
     *
     * @param requisitionId 领用单ID
     * @return 操作结果
     */
    @PutMapping("/{requisitionId}/submit")
    public AjaxResult submit(@PathVariable String requisitionId) {
        try {
            log.info("提交领用单：{}", requisitionId);
            String username = SecurityUtils.getUsername();
            itemRequisitionService.submitRequisition(requisitionId, username);
            return AjaxResult.success("提交领用单成功");
        } catch (Exception e) {
            log.error("提交领用单异常：", e);
            return AjaxResult.error("提交领用单失败：" + e.getMessage());
        }
    }

    /**
     * 确认领用单
     *
     * @param requisitionId 领用单ID
     * @param remark 确认备注
     * @return 操作结果
     */
    @PutMapping("/{requisitionId}/confirm")
    public AjaxResult confirm(@PathVariable String requisitionId, @RequestParam(required = false) String remark) {
        try {
            log.info("确认领用单：{}，备注：{}", requisitionId, remark);
            String username = SecurityUtils.getUsername();
            itemRequisitionService.confirmRequisition(requisitionId, remark, username);
            return AjaxResult.success("确认领用单成功");
        } catch (Exception e) {
            log.error("确认领用单异常：", e);
            return AjaxResult.error("确认领用单失败：" + e.getMessage());
        }
    }

    /**
     * 审核领用单
     *
     * @param requisitionId 领用单ID
     * @param status 审核结果(4-通过, 5-退回)
     * @param remark 审核备注
     * @return 操作结果
     */
    @PutMapping("/{requisitionId}/audit")
    public AjaxResult audit(@PathVariable String requisitionId, 
                           @RequestParam Integer status, 
                           @RequestParam(required = false) String remark) {
        try {
            log.info("审核领用单：{}，结果：{}，备注：{}", requisitionId, status, remark);
            String username = SecurityUtils.getUsername();
            itemRequisitionService.auditRequisition(requisitionId, status, remark, username);
            return AjaxResult.success("审核领用单成功");
        } catch (Exception e) {
            log.error("审核领用单异常：", e);
            return AjaxResult.error("审核领用单失败：" + e.getMessage());
        }
    }

    /**
     * 完成领用
     *
     * @param requisitionId 领用单ID
     * @return 操作结果
     */
    @PutMapping("/{requisitionId}/complete")
    public AjaxResult complete(@PathVariable String requisitionId) {
        try {
            log.info("完成领用：{}", requisitionId);
            String username = SecurityUtils.getUsername();
            itemRequisitionService.completeRequisition(requisitionId, username);
            return AjaxResult.success("完成领用成功");
        } catch (Exception e) {
            log.error("完成领用异常：", e);
            return AjaxResult.error("完成领用失败：" + e.getMessage());
        }
    }

    /**
     * 删除领用单
     *
     * @param requisitionIds 领用单ID字符串，多个以逗号分隔
     * @return 操作结果
     */
    @DeleteMapping("/{requisitionIds}")
    public AjaxResult remove(@PathVariable String requisitionIds) {
        try {
            log.info("删除领用单：{}", requisitionIds);
            String username = SecurityUtils.getUsername();
            String[] ids = requisitionIds.split(",");
            itemRequisitionService.deleteRequisition(ids, username);
            return AjaxResult.success("删除领用单成功");
        } catch (Exception e) {
            log.error("删除领用单异常：", e);
            return AjaxResult.error("删除领用单失败：" + e.getMessage());
        }
    }

    /**
     * 查询领用单列表
     *
     * @param request 查询条件
     * @return 分页列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody ItemRequisitionSearchRequest request) {
        try {
            IPage<ItemRequisitionVo> page = itemRequisitionService.selectRequisitionList(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询领用单列表异常：", e);
            return AjaxResult.error("查询领用单列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询领用单详情
     *
     * @param requisitionId 领用单ID
     * @return 领用单详情
     */
    @GetMapping("/{requisitionId}")
    public AjaxResult getDetail(@PathVariable String requisitionId) {
        try {
            log.info("查询领用单详情：{}", requisitionId);
            ItemRequisitionDetailVo detail = itemRequisitionService.getRequisitionDetail(requisitionId);
            if (detail == null) {
                return AjaxResult.error("领用单不存在");
            }
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("查询领用单详情异常：", e);
            return AjaxResult.error("查询领用单详情失败：" + e.getMessage());
        }
    }
} 
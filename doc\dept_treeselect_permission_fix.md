# 部门树选择器权限问题修复方案

## 问题描述

非管理员角色用户在物品领用页面选择申请人后，申请部门无法自动填充。控制台显示：
- `system/dept/list` 接口正常返回部门数据
- `system/dept/treeselect` 接口返回空数组 `data: []`

## 问题分析

### 根本原因：数据权限控制

1. **接口差异**：
   - `/system/dept/list` 接口有 `@PreAuthorize("@ss.hasPermi('system:dept:list')")` 权限注解
   - `/system/dept/treeselect` 接口没有权限注解，但受数据权限控制

2. **数据权限机制**：
   ```java
   // SysDeptServiceImpl.selectDeptList方法
   @Override
   @DataScope(deptAlias = "d")  // 关键：数据权限控制
   public List<SysDept> selectDeptList(SysDept dept) {
       return deptMapper.selectDeptList(dept);
   }
   ```

3. **权限过滤逻辑**：
   - `@DataScope` 注解会根据用户角色的 `data_scope` 字段进行数据过滤
   - 如果角色的数据权限配置不当，会导致查询结果为空

### 数据权限类型说明

| 权限值 | 权限名称 | 说明 |
|--------|----------|------|
| 1 | 所有数据权限 | 可以看到所有部门数据 |
| 2 | 自定义数据权限 | 只能看到sys_role_dept表中配置的特定部门 |
| 3 | 本部门数据权限 | 只能看到自己所在部门 |
| 4 | 本部门及以下数据权限 | 可以看到自己部门及下级部门 |
| 5 | 仅本人数据权限 | 只能看到与自己相关的数据 |

## 解决方案

### 方案一：调整角色数据权限（推荐）

#### 1. 诊断当前权限配置
```sql
-- 查看所有角色的数据权限配置
SELECT 
    r.role_id, r.role_name, r.role_key, r.data_scope,
    CASE r.data_scope 
        WHEN '1' THEN '所有数据权限'
        WHEN '2' THEN '自定义数据权限'
        WHEN '3' THEN '本部门数据权限'
        WHEN '4' THEN '本部门及以下数据权限'
        WHEN '5' THEN '仅本人数据权限'
        ELSE '未知权限'
    END as data_scope_name
FROM sys_role r WHERE r.del_flag = '0';
```

#### 2. 修复数据权限
```sql
-- 为非管理员角色设置"所有数据权限"
UPDATE sys_role 
SET data_scope = '1' 
WHERE role_id != 1 AND del_flag = '0';
```

### 方案二：为特定角色设置合适权限

#### 1. 设置本部门及以下权限
```sql
-- 适用于希望用户只能看到自己部门及下级部门的场景
UPDATE sys_role 
SET data_scope = '4' 
WHERE role_id = 2;  -- 替换为实际的角色ID
```

#### 2. 设置自定义权限
```sql
-- 设置为自定义权限
UPDATE sys_role SET data_scope = '2' WHERE role_id = 2;

-- 为角色分配具体部门权限
DELETE FROM sys_role_dept WHERE role_id = 2;
INSERT INTO sys_role_dept (role_id, dept_id)
SELECT 2, dept_id FROM sys_dept WHERE del_flag = '0';
```

### 方案三：修改接口实现（不推荐）

如果不想修改数据权限，可以考虑修改 `treeselect` 接口：

```java
// 在SysDeptController中添加权限注解
@PreAuthorize("@ss.hasPermi('system:dept:list')")
@GetMapping("/treeselect")
public AjaxResult treeselect(SysDept dept) {
    List<TreeSelect> depts = deptService.selectDeptTreeList(dept);
    return success(depts);
}
```

但这种方案可能会影响其他功能，不建议使用。

## 修复步骤

### 1. 执行诊断SQL
运行 `doc/dept_data_scope_fix.sql` 中的诊断查询，了解当前权限配置。

### 2. 选择合适的修复方案
根据业务需求选择：
- **物品领用场景**：建议使用"所有数据权限"(1)，让用户能选择任意部门
- **安全要求较高**：使用"本部门及以下权限"(4)或"自定义权限"(2)

### 3. 执行修复SQL
```sql
-- 推荐方案：为非管理员角色设置所有数据权限
UPDATE sys_role 
SET data_scope = '1' 
WHERE role_id != 1 AND del_flag = '0';
```

### 4. 验证修复结果
1. **重启后端应用**（数据权限可能有缓存）
2. **重新登录**非管理员账号
3. **测试功能**：
   - 访问物品领用页面
   - 选择申请人
   - 验证申请部门是否自动填充

## 技术原理

### DataScope注解工作原理

1. **AOP拦截**：`DataScopeAspect` 拦截带有 `@DataScope` 注解的方法
2. **权限检查**：根据用户角色的 `data_scope` 字段生成SQL过滤条件
3. **SQL注入**：将过滤条件注入到查询参数中
4. **数据过滤**：数据库查询时应用过滤条件

### 关键代码分析

```java
// DataScopeAspect.java - 数据权限过滤逻辑
public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String deptAlias, String userAlias, String permission) {
    // 根据角色的dataScope生成不同的SQL条件
    if (DATA_SCOPE_ALL.equals(dataScope)) {
        // 所有数据权限：不添加过滤条件
    } else if (DATA_SCOPE_DEPT.equals(dataScope)) {
        // 本部门权限：只能看到自己部门
        sqlString.append(StringUtils.format(" OR {}.dept_id = {} ", deptAlias, user.getDeptId()));
    }
    // ... 其他权限类型的处理
}
```

## 预期效果

修复完成后：

1. **treeselect接口**正常返回部门树数据
2. **自动填充功能**正常工作
3. **权限控制**仍然有效，但不会阻止正常的业务功能
4. **用户体验**得到改善

## 注意事项

1. **安全考虑**：设置"所有数据权限"会让用户看到所有部门，请根据实际安全需求决定
2. **缓存清理**：修改数据权限后建议重启应用，清除可能的权限缓存
3. **测试覆盖**：建议对不同角色的用户进行全面测试
4. **备份数据**：修改前建议备份 `sys_role` 表数据

## 故障排除

### 如果修复后仍有问题

1. **检查用户部门**：确保用户有正确的部门分配
```sql
SELECT user_id, user_name, dept_id FROM sys_user WHERE user_id = ?;
```

2. **检查角色分配**：确保用户有正确的角色分配
```sql
SELECT u.user_name, r.role_name, r.data_scope 
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_id = ?;
```

3. **检查部门数据**：确保部门数据正常
```sql
SELECT dept_id, dept_name, parent_id, status FROM sys_dept WHERE del_flag = '0';
```

### 回滚方案

如果修复后出现其他问题，可以回滚：
```sql
-- 恢复为本部门数据权限
UPDATE sys_role SET data_scope = '3' WHERE role_id != 1;
```

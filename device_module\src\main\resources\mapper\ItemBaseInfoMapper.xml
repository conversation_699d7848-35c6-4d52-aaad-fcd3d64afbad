<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemBaseInfoMapper">

    <select id="selectItemList" resultType="com.jingfang.wh_item.module.vo.ItemVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            t1.spec_model,
            t1.unit,
            COALESCE(t1.total_stock, 0) as total_quantity,
            COALESCE(t1.safety_stock, 0) as safety_stock,
            CASE 
                WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                ELSE 1
            END as min_stock_status,
            CASE 
                WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN '正常'
                WHEN t1.total_stock &lt; t1.safety_stock THEN '不足'
                WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN '过剩'
                ELSE '正常'
            END as stock_status_name,
            COALESCE(t2.warehouse_count, 0) as warehouse_count,
            t3.has_expiry,
            t1.create_time,
            -- 新增仓库和货架位置信息
            t4.warehouse_id,
            t4.shelf_location,
            COALESCE(t4.current_quantity, 0) as available_quantity
        FROM 
            item_base_info t1
            LEFT JOIN (
                SELECT 
                    item_id,
                    COUNT(DISTINCT warehouse_id) as warehouse_count
                FROM item_inventory 
                GROUP BY item_id
            ) t2 ON t1.item_id = t2.item_id
            LEFT JOIN item_consumable_attr t3 ON t1.item_id = t3.item_id AND t1.item_type = 1
            -- 关联库存表获取仓库和货架位置信息（优先选择库存最多的仓库）
            LEFT JOIN (
                SELECT 
                    inv.item_id,
                    inv.warehouse_id,
                    inv.shelf_location,
                    inv.current_quantity,
                    ROW_NUMBER() OVER (PARTITION BY inv.item_id ORDER BY inv.current_quantity DESC, inv.warehouse_id ASC) as rn
                FROM item_inventory inv
                WHERE inv.current_quantity > 0
            ) t4 ON t1.item_id = t4.item_id AND t4.rn = 1
        WHERE 
            t1.deleted = 0
        <if test="request.itemId != null and request.itemId != ''">
            AND t1.item_id = #{request.itemId}
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND t1.item_name LIKE CONCAT('%', #{request.itemName}, '%')
        </if>
        <if test="request.itemCode != null and request.itemCode != ''">
            AND t1.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
        </if>
        <if test="request.itemType != null">
            AND t1.item_type = #{request.itemType}
        </if>
        <if test="request.specModel != null and request.specModel != ''">
            AND t1.spec_model LIKE CONCAT('%', #{request.specModel}, '%')
        </if>
        <if test="request.stockStatus != null">
            AND (
                CASE 
                    WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                    WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                    WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                    ELSE 1
                END
            ) = #{request.stockStatus}
        </if>
        <if test="request.warehouseId != null">
            AND EXISTS (
                SELECT 1 FROM item_inventory inv 
                WHERE inv.item_id = t1.item_id 
                AND inv.warehouse_id = #{request.warehouseId}
            )
        </if>
        <if test="request.hasExpiry != null">
            AND t3.has_expiry = #{request.hasExpiry}
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <!-- 查询物品基本信息（不包含库存） -->
    <select id="selectItemBaseInfoById" resultType="com.jingfang.wh_item.module.vo.ItemDetailVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            CASE t1.item_type WHEN 1 THEN '消耗品' WHEN 2 THEN '备品备件' ELSE '未知' END as item_type_name,
            t1.spec_model,
            t1.unit,
            t1.image_url,
            COALESCE(t1.total_stock, 0) as total_quantity,
            COALESCE(t1.safety_stock, 0) as safety_stock,
            t3.has_expiry,
            t3.expiry_period,
            t3.production_date,
            t3.storage_condition,
            t4.applicable_device,
            t4.part_category,
            CASE t4.part_category WHEN 1 THEN '关键' WHEN 2 THEN '常用' WHEN 3 THEN '次要' ELSE '未知' END as part_category_name,
            t4.replacement_cycle,
            t4.maintenance_history,
            t1.remark,
            t1.create_time,
            t1.create_by
        FROM 
            item_base_info t1
            LEFT JOIN item_consumable_attr t3 ON t1.item_id = t3.item_id AND t1.item_type = 1
            LEFT JOIN item_part_attr t4 ON t1.item_id = t4.item_id AND t1.item_type = 2
        WHERE 
            t1.item_id = #{itemId}
            AND t1.deleted = 0
    </select>

    <!-- 查询物品库存列表 -->
    <select id="selectItemInventoryList" resultType="com.jingfang.wh_item.module.vo.ItemInventoryVo">
        SELECT
            t1.inventory_id,
            t1.item_id,
            t1.warehouse_id,
            t1.current_quantity,
            t1.safety_stock,
            t1.stock_status,
            CASE t1.stock_status WHEN 1 THEN '正常' WHEN 2 THEN '不足' WHEN 3 THEN '过剩' ELSE '未知' END as stock_status_name,
            t1.shelf_location
        FROM
            item_inventory t1
        WHERE
            t1.item_id = #{itemId}
            AND t1.current_quantity != 0.00
            AND t1.warehouse_id IS NOT NULL
        ORDER BY t1.warehouse_id
    </select>
    
    <!-- 查询物品的仓库ID列表 -->
    <select id="selectWarehouseIdsByItemId" resultType="java.lang.Integer">
        SELECT DISTINCT warehouse_id
        FROM item_inventory
        WHERE item_id = #{itemId}
        ORDER BY warehouse_id
    </select>
    
    <!-- 物品库存统计查询 -->
    <select id="selectItemStockStatistics" resultType="com.jingfang.wh_item.module.vo.ItemStockStatisticsVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            CASE t1.item_type 
                WHEN 1 THEN '消耗品' 
                WHEN 2 THEN '备品备件' 
                ELSE '未知' 
            END as item_type_name,
            t1.spec_model,
            t1.unit,
            t2.warehouse_id,
            t2.shelf_location,
            COALESCE(t2.current_quantity, 0) as current_quantity,
            <choose>
                <!-- 按仓库统计：使用仓库级安全库存 -->
                <when test="request.statisticsType == 1">
                    COALESCE(t2.safety_stock, 0) as safety_stock,
                    COALESCE(t2.stock_status, 1) as stock_status,
                    CASE COALESCE(t2.stock_status, 1) 
                        WHEN 1 THEN '正常' 
                        WHEN 2 THEN '不足' 
                        WHEN 3 THEN '过剩' 
                        ELSE '未知' 
                    END as stock_status_name
                </when>
                <!-- 按物品统计：使用企业级安全库存 -->
                <otherwise>
                    COALESCE(t1.safety_stock, 0) as safety_stock,
                    CASE 
                        WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                        WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                        WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                        ELSE 1
                    END as stock_status,
                    CASE 
                        WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN '正常'
                        WHEN t1.total_stock &lt; t1.safety_stock THEN '不足'
                        WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN '过剩'
                        ELSE '正常'
                    END as stock_status_name
                </otherwise>
            </choose>
        FROM 
            item_base_info t1
        <choose>
            <!-- 按仓库统计：显示每个物品在每个仓库的库存情况 -->
            <when test="request.statisticsType == 1">
                LEFT JOIN item_inventory t2 ON t1.item_id = t2.item_id
            </when>
            <!-- 按物品统计：汇总每个物品在所有仓库的库存 -->
            <otherwise>
                LEFT JOIN (
                    SELECT 
                        item_id,
                        MIN(warehouse_id) as warehouse_id,
                        GROUP_CONCAT(DISTINCT shelf_location ORDER BY shelf_location SEPARATOR ', ') as shelf_location,
                        SUM(current_quantity) as current_quantity
                    FROM item_inventory 
                    GROUP BY item_id
                ) t2 ON t1.item_id = t2.item_id
            </otherwise>
        </choose>
        WHERE 
            t1.deleted = 0
        <if test="request.itemId != null and request.itemId != ''">
            AND t1.item_id = #{request.itemId}
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND t1.item_name LIKE CONCAT('%', #{request.itemName}, '%')
        </if>
        <if test="request.itemCode != null and request.itemCode != ''">
            AND t1.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
        </if>
        <if test="request.itemType != null">
            AND t1.item_type = #{request.itemType}
        </if>
        <if test="request.specModel != null and request.specModel != ''">
            AND t1.spec_model LIKE CONCAT('%', #{request.specModel}, '%')
        </if>
        <if test="request.warehouseId != null">
            <choose>
                <when test="request.statisticsType == 1">
                    AND t2.warehouse_id = #{request.warehouseId}
                </when>
                <otherwise>
                    AND EXISTS (
                        SELECT 1 FROM item_inventory inv 
                        WHERE inv.item_id = t1.item_id 
                        AND inv.warehouse_id = #{request.warehouseId}
                    )
                </otherwise>
            </choose>
        </if>
        <if test="request.stockStatus != null">
            <choose>
                <when test="request.statisticsType == 1">
                    AND t2.stock_status = #{request.stockStatus}
                </when>
                <otherwise>
                    AND (
                        CASE 
                            WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                            WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                            WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                            ELSE 1
                        END
                    ) = #{request.stockStatus}
                </otherwise>
            </choose>
        </if>
        <if test="request.onlyWithStock != null and request.onlyWithStock == true">
            <choose>
                <when test="request.statisticsType == 1">
                    AND t2.current_quantity &gt; 0
                </when>
                <otherwise>
                    AND t1.total_stock &gt; 0
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="request.statisticsType == 1">
                ORDER BY t2.warehouse_id, t1.item_name
            </when>
            <otherwise>
                ORDER BY t1.item_id
            </otherwise>
        </choose>
    </select>

    <!-- 物品库存统计查询（无分页） -->
    <select id="selectItemStockStatisticsWithoutPage" resultType="com.jingfang.wh_item.module.vo.ItemStockStatisticsVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            t1.safety_stock,
            CASE t1.item_type 
                WHEN 1 THEN '消耗品' 
                WHEN 2 THEN '备品备件' 
                ELSE '未知' 
            END as item_type_name,
            t1.spec_model,
            t1.unit,
            t2.warehouse_id,
            t2.shelf_location,
            COALESCE(t2.current_quantity, 0) as current_quantity,
            <choose>
                <!-- 按仓库统计：使用仓库级安全库存 -->
                <when test="request.statisticsType == 1">
                    COALESCE(t2.safety_stock, 0) as safety_stock,
                    COALESCE(t2.stock_status, 1) as stock_status,
                    CASE COALESCE(t2.stock_status, 1) 
                        WHEN 1 THEN '正常' 
                        WHEN 2 THEN '不足' 
                        WHEN 3 THEN '过剩' 
                        ELSE '未知' 
                    END as stock_status_name
                </when>
                <!-- 按物品统计：使用企业级安全库存 -->
                <otherwise>
                    COALESCE(t1.safety_stock, 0) as safety_stock,
                    CASE 
                        WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                        WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                        WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                        ELSE 1
                    END as stock_status,
                    CASE 
                        WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN '正常'
                        WHEN t1.total_stock &lt; t1.safety_stock THEN '不足'
                        WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN '过剩'
                        ELSE '正常'
                    END as stock_status_name
                </otherwise>
            </choose>
        FROM 
            item_base_info t1
        <choose>
            <!-- 按仓库统计：显示每个物品在每个仓库的库存情况 -->
            <when test="request.statisticsType == 1">
                LEFT JOIN item_inventory t2 ON t1.item_id = t2.item_id
            </when>
            <!-- 按物品统计：汇总每个物品在所有仓库的库存 -->
            <otherwise>
                LEFT JOIN (
                    SELECT 
                        item_id,
                        MIN(warehouse_id) as warehouse_id,
                        GROUP_CONCAT(DISTINCT shelf_location ORDER BY shelf_location SEPARATOR ', ') as shelf_location,
                        SUM(current_quantity) as current_quantity
                    FROM item_inventory 
                    GROUP BY item_id
                ) t2 ON t1.item_id = t2.item_id
            </otherwise>
        </choose>
        WHERE 
            t1.deleted = 0
        <if test="request.itemId != null and request.itemId != ''">
            AND t1.item_id = #{request.itemId}
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND t1.item_name LIKE CONCAT('%', #{request.itemName}, '%')
        </if>
        <if test="request.itemCode != null and request.itemCode != ''">
            AND t1.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
        </if>
        <if test="request.itemType != null">
            AND t1.item_type = #{request.itemType}
        </if>
        <if test="request.specModel != null and request.specModel != ''">
            AND t1.spec_model LIKE CONCAT('%', #{request.specModel}, '%')
        </if>
        <if test="request.warehouseId != null">
            <choose>
                <when test="request.statisticsType == 1">
                    AND t2.warehouse_id = #{request.warehouseId}
                </when>
                <otherwise>
                    AND EXISTS (
                        SELECT 1 FROM item_inventory inv 
                        WHERE inv.item_id = t1.item_id 
                        AND inv.warehouse_id = #{request.warehouseId}
                    )
                </otherwise>
            </choose>
        </if>
        <if test="request.stockStatus != null">
            <choose>
                <when test="request.statisticsType == 1">
                    AND t2.stock_status = #{request.stockStatus}
                </when>
                <otherwise>
                    AND (
                        CASE 
                            WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                            WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                            WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                            ELSE 1
                        END
                    ) = #{request.stockStatus}
                </otherwise>
            </choose>
        </if>
        <if test="request.onlyWithStock != null and request.onlyWithStock == true">
            <choose>
                <when test="request.statisticsType == 1">
                    AND t2.current_quantity &gt; 0
                </when>
                <otherwise>
                    AND t1.total_stock &gt; 0
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="request.statisticsType == 1">
                ORDER BY t2.warehouse_id, t1.item_name
            </when>
            <otherwise>
                ORDER BY t1.item_id
            </otherwise>
        </choose>
    </select>
    
    <!-- 查询库存不足的物品列表（库存下限告警） -->
    <select id="selectLowStockItems" resultType="com.jingfang.wh_item.module.vo.ItemVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            t1.spec_model,
            t1.unit,
            COALESCE(t1.total_stock, 0) as total_quantity,
            COALESCE(t1.safety_stock, 0) as safety_stock,
            2 as min_stock_status,
            '不足' as stock_status_name,
            COALESCE(t2.warehouse_count, 0) as warehouse_count,
            t3.has_expiry,
            t1.create_time
        FROM 
            item_base_info t1
            LEFT JOIN (
                SELECT 
                    item_id,
                    COUNT(DISTINCT warehouse_id) as warehouse_count
                FROM item_inventory 
                GROUP BY item_id
            ) t2 ON t1.item_id = t2.item_id
            LEFT JOIN item_consumable_attr t3 ON t1.item_id = t3.item_id AND t1.item_type = 1
        WHERE 
            t1.deleted = 0
            AND t1.safety_stock &gt; 0
            AND t1.total_stock &lt; t1.safety_stock
        ORDER BY 
            (t1.safety_stock - t1.total_stock) DESC, t1.item_name
    </select>
    
    <!-- 库存查看 - 按仓库展示 -->
    <select id="selectItemStockViewByWarehouse" resultType="com.jingfang.wh_item.module.vo.ItemStockViewVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            CASE t1.item_type 
                WHEN 1 THEN '消耗品' 
                WHEN 2 THEN '备品备件' 
                ELSE '未知' 
            END as item_type_name,
            t1.spec_model,
            t1.unit,
            t2.warehouse_id,
            t2.shelf_location,
            COALESCE(t2.current_quantity, 0) as current_quantity,
            COALESCE(t2.safety_stock, 0) as safety_stock,
            COALESCE(t1.safety_stock, 0) as enterprise_safety_stock,
            COALESCE(t1.total_stock, 0) as total_quantity,
            COALESCE(t2.stock_status, 1) as stock_status,
            CASE COALESCE(t2.stock_status, 1) 
                WHEN 1 THEN '正常' 
                WHEN 2 THEN '不足' 
                WHEN 3 THEN '过剩' 
                ELSE '未知' 
            END as stock_status_name
        FROM 
            item_base_info t1
            INNER JOIN item_inventory t2 ON t1.item_id = t2.item_id
        WHERE 
            t1.deleted = 0
        <if test="request.itemId != null and request.itemId != ''">
            AND t1.item_id = #{request.itemId}
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND t1.item_name LIKE CONCAT('%', #{request.itemName}, '%')
        </if>
        <if test="request.itemCode != null and request.itemCode != ''">
            AND t1.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
        </if>
        <if test="request.itemType != null">
            AND t1.item_type = #{request.itemType}
        </if>
        <if test="request.specModel != null and request.specModel != ''">
            AND t1.spec_model LIKE CONCAT('%', #{request.specModel}, '%')
        </if>
        <if test="request.warehouseId != null">
            AND t2.warehouse_id = #{request.warehouseId}
        </if>
        <if test="request.stockStatus != null">
            AND t2.stock_status = #{request.stockStatus}
        </if>
        <if test="request.onlyWithStock != null and request.onlyWithStock == true">
            AND t2.current_quantity > 0
        </if>
        ORDER BY t2.warehouse_id, t1.item_name
    </select>
    
    <!-- 库存查看 - 按物品展示 -->
    <select id="selectItemStockViewByItem" resultType="com.jingfang.wh_item.module.vo.ItemStockViewVo">
        SELECT 
            t1.item_id,
            t1.item_name,
            t1.item_code,
            t1.item_type,
            CASE t1.item_type 
                WHEN 1 THEN '消耗品' 
                WHEN 2 THEN '备品备件' 
                ELSE '未知' 
            END as item_type_name,
            t1.spec_model,
            t1.unit,
            COALESCE(t1.safety_stock, 0) as enterprise_safety_stock,
            COALESCE(t1.total_stock, 0) as total_quantity,
            CASE 
                WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                ELSE 1
            END as stock_status,
            CASE 
                WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN '正常'
                WHEN t1.total_stock &lt; t1.safety_stock THEN '不足'
                WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN '过剩'
                ELSE '正常'
            END as stock_status_name
        FROM 
            item_base_info t1
        WHERE 
            t1.deleted = 0
            AND EXISTS (
                SELECT 1 FROM item_inventory inv 
                WHERE inv.item_id = t1.item_id
                <if test="request.onlyWithStock != null and request.onlyWithStock == true">
                    AND inv.current_quantity > 0
                </if>
            )
        <if test="request.itemId != null and request.itemId != ''">
            AND t1.item_id = #{request.itemId}
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND t1.item_name LIKE CONCAT('%', #{request.itemName}, '%')
        </if>
        <if test="request.itemCode != null and request.itemCode != ''">
            AND t1.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
        </if>
        <if test="request.itemType != null">
            AND t1.item_type = #{request.itemType}
        </if>
        <if test="request.specModel != null and request.specModel != ''">
            AND t1.spec_model LIKE CONCAT('%', #{request.specModel}, '%')
        </if>
        <if test="request.warehouseId != null">
            AND EXISTS (
                SELECT 1 FROM item_inventory inv 
                WHERE inv.item_id = t1.item_id 
                AND inv.warehouse_id = #{request.warehouseId}
            )
        </if>
        <if test="request.stockStatus != null">
            AND (
                CASE 
                    WHEN t1.safety_stock IS NULL OR t1.safety_stock &lt;= 0 THEN 1
                    WHEN t1.total_stock &lt; t1.safety_stock THEN 2
                    WHEN t1.total_stock &gt; t1.safety_stock * 2 THEN 3
                    ELSE 1
                END
            ) = #{request.stockStatus}
        </if>
        ORDER BY t1.item_name
    </select>
    
    <!-- 查询物品的库存详情列表（用于按物品展示模式） -->
    <select id="selectItemStockDetails" resultType="com.jingfang.wh_item.module.vo.ItemStockViewVo$ItemStockDetailVo">
        SELECT 
            t1.warehouse_id,
            t1.shelf_location,
            COALESCE(t1.current_quantity, 0) as current_quantity,
            COALESCE(t1.safety_stock, 0) as safety_stock,
            COALESCE(t1.stock_status, 1) as stock_status,
            CASE COALESCE(t1.stock_status, 1) 
                WHEN 1 THEN '正常' 
                WHEN 2 THEN '不足' 
                WHEN 3 THEN '过剩' 
                ELSE '未知' 
            END as stock_status_name
        FROM 
            item_inventory t1
        WHERE 
            t1.item_id = #{itemId}
        ORDER BY t1.warehouse_id
    </select>
</mapper> 
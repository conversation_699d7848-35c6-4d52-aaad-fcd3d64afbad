package com.jingfang.web.controller.asset.workbench;

import com.jingfang.asset_ledger.module.vo.AssetWorkbenchVo;
import com.jingfang.asset_ledger.service.AssetWorkbenchService;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 资产工作台控制器
 */
@Slf4j
@Api(tags = "资产工作台管理")
@RestController
@RequestMapping("/asset/workbench")
public class AssetWorkbenchController extends BaseController {
    
    @Resource
    private AssetWorkbenchService assetWorkbenchService;
    
    /**
     * 获取资产工作台完整数据
     */
    @ApiOperation("获取资产工作台完整数据")
    @GetMapping("/data")
    public AjaxResult getWorkbenchData() {
        try {
            AssetWorkbenchVo workbenchData = assetWorkbenchService.getWorkbenchData();
            return AjaxResult.success(workbenchData);
        } catch (Exception e) {
            log.error("获取资产工作台数据失败", e);
            return AjaxResult.error("获取工作台数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取资产概览数据
     */
    @ApiOperation("获取资产概览数据")
    @GetMapping("/overview")
    public AjaxResult getAssetOverview() {
        try {
            AssetWorkbenchVo.AssetOverviewVo overview = assetWorkbenchService.getAssetOverview();
            return AjaxResult.success(overview);
        } catch (Exception e) {
            log.error("获取资产概览数据失败", e);
            return AjaxResult.error("获取概览数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取资产趋势数据
     */
    @ApiOperation("获取资产趋势数据")
    @GetMapping("/trends")
    public AjaxResult getAssetTrends(
            @ApiParam(value = "天数", example = "30") 
            @RequestParam(defaultValue = "30") int days) {
        try {
            if (days <= 0 || days > 365) {
                return AjaxResult.error("天数参数无效，应在1-365之间");
            }
            
            AssetWorkbenchVo.AssetTrendVo trends = assetWorkbenchService.getAssetTrends(days);
            return AjaxResult.success(trends);
        } catch (Exception e) {
            log.error("获取资产趋势数据失败", e);
            return AjaxResult.error("获取趋势数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取入库出库统计数据
     */
    @ApiOperation("获取入库出库统计数据")
    @GetMapping("/inout-statistics")
    public AjaxResult getInOutStatistics() {
        try {
            AssetWorkbenchVo.AssetInOutStatisticsVo statistics = assetWorkbenchService.getInOutStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取入库出库统计数据失败", e);
            return AjaxResult.error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取处置统计数据
     */
    @ApiOperation("获取处置统计数据")
    @GetMapping("/disposal-statistics")
    public AjaxResult getDisposalStatistics() {
        try {
            AssetWorkbenchVo.AssetDisposalStatisticsVo statistics = assetWorkbenchService.getDisposalStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取处置统计数据失败", e);
            return AjaxResult.error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 刷新工作台缓存数据
     */
    @ApiOperation("刷新工作台缓存数据")
    @PostMapping("/refresh")
    public AjaxResult refreshWorkbenchData() {
        try {
            // 这里可以添加缓存刷新逻辑
            log.info("用户{}刷新了资产工作台数据", getUsername());
            return AjaxResult.success("刷新成功");
        } catch (Exception e) {
            log.error("刷新工作台数据失败", e);
            return AjaxResult.error("刷新失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取资产状态分布数据
     */
    @ApiOperation("获取资产状态分布数据")
    @GetMapping("/status-distribution")
    public AjaxResult getStatusDistribution() {
        try {
            AssetWorkbenchVo.AssetOverviewVo overview = assetWorkbenchService.getAssetOverview();
            
            // 构造状态分布数据
            java.util.Map<String, Object> statusData = new java.util.HashMap<>();
            statusData.put("normal", overview.getNormalStatusCount());
            statusData.put("repair", overview.getRepairStatusCount());
            statusData.put("scrap", overview.getScrapStatusCount());
            statusData.put("utilizationRate", overview.getUtilizationRate());
            
            return AjaxResult.success(statusData);
        } catch (Exception e) {
            log.error("获取资产状态分布数据失败", e);
            return AjaxResult.error("获取状态分布数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取资产价值统计
     */
    @ApiOperation("获取资产价值统计")
    @GetMapping("/value-statistics")
    public AjaxResult getValueStatistics() {
        try {
            AssetWorkbenchVo.AssetOverviewVo overview = assetWorkbenchService.getAssetOverview();
            
            // 构造价值统计数据
            java.util.Map<String, Object> valueData = new java.util.HashMap<>();
            valueData.put("totalValue", overview.getTotalValue());
            valueData.put("monthlyNewValue", overview.getMonthlyNewValue());
            valueData.put("totalCount", overview.getTotalCount());
            valueData.put("monthlyNewCount", overview.getMonthlyNewCount());
            
            return AjaxResult.success(valueData);
        } catch (Exception e) {
            log.error("获取资产价值统计数据失败", e);
            return AjaxResult.error("获取价值统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取待办事项统计
     */
    @ApiOperation("获取待办事项统计")
    @GetMapping("/todo-statistics")
    public AjaxResult getTodoStatistics() {
        try {
            AssetWorkbenchVo.AssetOverviewVo overview = assetWorkbenchService.getAssetOverview();
            AssetWorkbenchVo.AssetInOutStatisticsVo inOutStats = assetWorkbenchService.getInOutStatistics();
            AssetWorkbenchVo.AssetDisposalStatisticsVo disposalStats = assetWorkbenchService.getDisposalStatistics();
            
            // 构造待办事项数据
            java.util.Map<String, Object> todoData = new java.util.HashMap<>();
            todoData.put("pendingDisposal", overview.getPendingDisposalCount());
            todoData.put("pendingConfirm", inOutStats.getPendingConfirmCount());
            todoData.put("pendingAudit", inOutStats.getPendingAuditCount());
            todoData.put("pendingApproval", disposalStats.getPendingApprovalCount());
            todoData.put("processing", disposalStats.getProcessingCount());
            
            return AjaxResult.success(todoData);
        } catch (Exception e) {
            log.error("获取待办事项统计数据失败", e);
            return AjaxResult.error("获取待办事项数据失败：" + e.getMessage());
        }
    }
}

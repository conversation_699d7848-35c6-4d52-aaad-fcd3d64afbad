-- 部门数据权限问题修复SQL脚本
-- 用于解决非管理员用户无法获取部门树数据的问题

-- 1. 查看当前角色的数据权限配置
SELECT 
    r.role_id,
    r.role_name,
    r.role_key,
    r.data_scope,
    CASE r.data_scope 
        WHEN '1' THEN '所有数据权限'
        WHEN '2' THEN '自定义数据权限'
        WHEN '3' THEN '本部门数据权限'
        WHEN '4' THEN '本部门及以下数据权限'
        WHEN '5' THEN '仅本人数据权限'
        ELSE '未知权限'
    END as data_scope_name,
    r.status
FROM sys_role r 
WHERE r.del_flag = '0'
ORDER BY r.role_id;

-- 2. 查看用户角色分配情况
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    d.dept_name,
    r.role_id,
    r.role_name,
    r.data_scope,
    CASE r.data_scope 
        WHEN '1' THEN '所有数据权限'
        WHEN '2' THEN '自定义数据权限'
        WHEN '3' THEN '本部门数据权限'
        WHEN '4' THEN '本部门及以下数据权限'
        WHEN '5' THEN '仅本人数据权限'
        ELSE '未知权限'
    END as data_scope_name
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.del_flag = '0' AND u.user_id != 1  -- 排除管理员
ORDER BY u.user_id, r.role_id;

-- 3. 修复方案一：为普通用户角色设置"所有数据权限"
-- 注意：这会让用户看到所有部门数据，请根据实际安全需求决定是否执行

-- 查找普通用户角色（通常是role_key为'common'或类似的角色）
SELECT role_id, role_name, role_key, data_scope FROM sys_role WHERE role_key LIKE '%common%' OR role_name LIKE '%普通%' OR role_name LIKE '%用户%';

-- 为普通用户角色设置所有数据权限（请根据实际角色ID调整）
-- UPDATE sys_role SET data_scope = '1' WHERE role_key = 'common';

-- 4. 修复方案二：为特定角色设置"本部门及以下数据权限"
-- 这样用户可以看到自己部门及下级部门的数据

-- 为角色ID为2的角色设置本部门及以下数据权限
-- UPDATE sys_role SET data_scope = '4' WHERE role_id = 2;

-- 5. 修复方案三：为特定用户的角色设置自定义数据权限
-- 需要同时在sys_role_dept表中配置具体的部门权限

-- 设置角色为自定义数据权限
-- UPDATE sys_role SET data_scope = '2' WHERE role_id = 2;

-- 为角色分配具体的部门权限（允许访问所有部门）
-- 首先删除现有的角色部门关联
-- DELETE FROM sys_role_dept WHERE role_id = 2;

-- 然后添加所有部门的访问权限
-- INSERT INTO sys_role_dept (role_id, dept_id)
-- SELECT 2, dept_id FROM sys_dept WHERE del_flag = '0';

-- 6. 推荐的修复方案：为非管理员角色设置合适的数据权限

-- 方案A：如果希望用户能看到所有部门（适用于需要选择任意部门的场景）
UPDATE sys_role 
SET data_scope = '1' 
WHERE role_id IN (
    SELECT DISTINCT ur.role_id 
    FROM sys_user_role ur 
    JOIN sys_user u ON ur.user_id = u.user_id 
    WHERE u.user_id != 1 AND u.del_flag = '0'
) AND role_id != 1;  -- 排除管理员角色

-- 方案B：如果希望用户只能看到自己部门及下级部门
-- UPDATE sys_role 
-- SET data_scope = '4' 
-- WHERE role_id IN (
--     SELECT DISTINCT ur.role_id 
--     FROM sys_user_role ur 
--     JOIN sys_user u ON ur.user_id = u.user_id 
--     WHERE u.user_id != 1 AND u.del_flag = '0'
-- ) AND role_id != 1;

-- 7. 验证修复结果
-- 查看修复后的角色数据权限配置
SELECT 
    r.role_id,
    r.role_name,
    r.role_key,
    r.data_scope,
    CASE r.data_scope 
        WHEN '1' THEN '所有数据权限'
        WHEN '2' THEN '自定义数据权限'
        WHEN '3' THEN '本部门数据权限'
        WHEN '4' THEN '本部门及以下数据权限'
        WHEN '5' THEN '仅本人数据权限'
        ELSE '未知权限'
    END as data_scope_name,
    r.status
FROM sys_role r 
WHERE r.del_flag = '0' AND r.role_id != 1
ORDER BY r.role_id;

-- 8. 测试查询（模拟非管理员用户查询部门数据）
-- 这个查询模拟了DataScope注解的效果
-- 请将{user_id}替换为实际的非管理员用户ID进行测试

-- SELECT d.dept_id, d.dept_name, d.parent_id, d.status
-- FROM sys_dept d
-- WHERE d.del_flag = '0' 
-- AND d.status = '0'
-- AND (
--     -- 模拟数据权限过滤条件
--     d.dept_id IN (
--         SELECT DISTINCT dept_id FROM sys_dept 
--         WHERE del_flag = '0' AND status = '0'
--     )
-- )
-- ORDER BY d.parent_id, d.order_num;

-- 9. 清理和重置（如果需要回滚）
-- 如果修复后出现问题，可以使用以下语句回滚

-- 将所有非管理员角色的数据权限重置为本部门数据权限
-- UPDATE sys_role SET data_scope = '3' WHERE role_id != 1 AND del_flag = '0';

-- 10. 说明和建议
/*
数据权限说明：
1 = 所有数据权限：用户可以看到所有部门的数据
2 = 自定义数据权限：用户只能看到sys_role_dept表中配置的特定部门数据
3 = 本部门数据权限：用户只能看到自己所在部门的数据
4 = 本部门及以下数据权限：用户可以看到自己部门及其下级部门的数据
5 = 仅本人数据权限：用户只能看到与自己相关的数据

建议：
- 对于物品领用等需要选择部门的功能，建议使用"所有数据权限"(1)或"本部门及以下数据权限"(4)
- 如果有严格的数据安全要求，可以使用"自定义数据权限"(2)并在sys_role_dept表中精确配置
- 避免使用"仅本人数据权限"(5)，因为这会导致部门选择功能无法正常工作
*/

package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.wh_item.module.dto.ItemDto;
import com.jingfang.wh_item.module.dto.ItemRegistrationDto;
import com.jingfang.wh_item.module.request.ItemSearchRequest;
import com.jingfang.wh_item.module.request.ItemStockStatisticsRequest;
import com.jingfang.wh_item.module.vo.ItemDetailVo;
import com.jingfang.wh_item.module.vo.ItemStockStatisticsVo;
import com.jingfang.wh_item.module.vo.ItemVo;
import com.jingfang.wh_item.service.ItemService;
import com.jingfang.wh_item.service.ItemStockSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物品信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/item")
public class ItemController extends BaseController {

    @Resource
    private ItemService itemService;

    @Resource
    private ItemStockSyncService itemStockSyncService;

    /**
     * 新增物品（包含库存信息，实际为物品单体入库）
     *
     * @param itemDto 物品信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ItemDto itemDto) {
        try {
            log.info("新增物品请求参数：{}", itemDto);
            boolean success = itemService.addItem(itemDto);
            if (success) {
                return AjaxResult.success("新增物品成功");
            } else {
                return AjaxResult.error("新增物品失败");
            }
        } catch (Exception e) {
            log.error("新增物品异常：", e);
            return AjaxResult.error("新增物品失败：" + e.getMessage());
        }
    }

    /**
     * 物品登记（只登记基本信息和属性，不包含库存信息）
     *
     * @param registrationDto 物品登记信息
     * @return 操作结果
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody ItemRegistrationDto registrationDto) {
        try {
            log.info("物品登记请求参数：{}", registrationDto);
            boolean success = itemService.registerItem(registrationDto);
            if (success) {
                return AjaxResult.success("物品登记成功");
            } else {
                return AjaxResult.error("物品登记失败");
            }
        } catch (Exception e) {
            log.error("物品登记异常：", e);
            return AjaxResult.error("物品登记失败：" + e.getMessage());
        }
    }

    /**
     * 修改物品
     *
     * @param itemDto 物品信息
     * @return 操作结果
     */
    @PutMapping
    public AjaxResult edit(@RequestBody ItemDto itemDto) {
        try {
            log.info("修改物品请求参数：{}", itemDto);
            boolean success = itemService.updateItem(itemDto);
            if (success) {
                return AjaxResult.success("修改物品成功");
            } else {
                return AjaxResult.error("修改物品失败");
            }
        } catch (Exception e) {
            log.error("修改物品异常：", e);
            return AjaxResult.error("修改物品失败：" + e.getMessage());
        }
    }

    /**
     * 删除物品
     *
     * @param itemIds 物品ID字符串，多个以逗号分隔
     * @return 操作结果
     */
    @DeleteMapping("/{itemIds}")
    public AjaxResult remove(@PathVariable String itemIds) {
        try {
            log.info("删除物品，ID：{}", itemIds);
            List<String> ids = Arrays.asList(itemIds.split(",")).stream().filter(s -> !s.isEmpty()).collect(Collectors.toList());
            boolean success = itemService.deleteItems(ids);
            if (success) {
                return AjaxResult.success("删除物品成功");
            } else {
                return AjaxResult.error("删除物品失败");
            }
        } catch (Exception e) {
            log.error("删除物品异常：", e);
            return AjaxResult.error("删除物品失败：" + e.getMessage());
        }
    }

    /**
     * 获取物品详情
     *
     * @param itemId 物品ID
     * @return 物品详情
     */
    @GetMapping("/{itemId}")
    public AjaxResult getDetail(@PathVariable String itemId) {
        try {
            log.info("获取物品详情，ID：{}", itemId);
            ItemDetailVo detail = itemService.getItemDetail(itemId);
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取物品详情异常：", e);
            return AjaxResult.error("获取物品详情失败：" + e.getMessage());
        }
    }

    /**
     * 物品列表
     *
     * @param request 查询条件
     * @return 分页列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody ItemSearchRequest request) {
        try {
            IPage<ItemVo> page = itemService.selectItemList(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询物品列表异常：", e);
            return AjaxResult.error("查询物品列表失败：" + e.getMessage());
        }
    }

    /**
     * 物品选择列表（用于领用等业务场景，包含仓库和货架位置信息）
     *
     * @param request 查询条件
     * @return 分页列表
     */
    @PostMapping("/select/list")
    public AjaxResult selectList(@RequestBody ItemSearchRequest request) {
        try {
            log.info("查询物品选择列表，请求参数：{}", request);
            IPage<ItemVo> page = itemService.selectItemList(request);
            log.info("查询物品选择列表成功，返回{}条记录", page.getRecords().size());
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询物品选择列表异常：", e);
            return AjaxResult.error("查询物品选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 更新物品库存
     *
     * @param itemId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 变动数量
     * @param operationType 操作类型
     * @param operationId 关联单据ID
     * @param remark 备注
     * @param shelfLocation 货架位置
     * @return 操作结果
     */
    @PutMapping("/stock")
    public AjaxResult updateStock(
            @RequestParam String itemId,
            @RequestParam Integer warehouseId,
            @RequestParam int quantity,
            @RequestParam int operationType,
            @RequestParam(required = false) String operationId,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false) String shelfLocation) {
        try {
            log.info("更新物品库存，itemId={}，warehouseId={}，quantity={}，operationType={}，shelfLocation={}", 
                    itemId, warehouseId, quantity, operationType, shelfLocation);
            boolean success = itemService.updateItemStock(itemId, warehouseId, quantity, operationType, operationId, remark, shelfLocation);
            if (success) {
                return AjaxResult.success("更新库存成功");
            } else {
                return AjaxResult.error("更新库存失败");
            }
        } catch (Exception e) {
            log.error("更新物品库存异常：", e);
            return AjaxResult.error("更新库存失败：" + e.getMessage());
        }
    }

    /**
     * 物品库存统计
     *
     * @param request 查询条件
     * @return 库存统计列表
     */
    @PostMapping("/stock/statistics")
    public AjaxResult stockStatistics(@RequestBody ItemStockStatisticsRequest request) {
        try {
            log.info("物品库存统计查询，请求参数：{}", request);
            IPage<ItemStockStatisticsVo> page = itemService.selectItemStockStatistics(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("物品库存统计查询异常：", e);
            return AjaxResult.error("库存统计查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新物品企业级安全库存
     *
     * @param itemId 物品ID
     * @param safetyStock 安全库存数量
     * @return 操作结果
     */
    @PutMapping("/safety-stock")
    public AjaxResult updateSafetyStock(
            @RequestParam String itemId,
            @RequestParam BigDecimal safetyStock) {
        try {
            log.info("更新物品企业级安全库存，itemId={}，safetyStock={}", itemId, safetyStock);
            boolean success = itemStockSyncService.updateSafetyStock(itemId, safetyStock);
            if (success) {
                return AjaxResult.success("更新企业级安全库存成功");
            } else {
                return AjaxResult.error("更新企业级安全库存失败，物品不存在");
            }
        } catch (Exception e) {
            log.error("更新物品企业级安全库存异常：", e);
            return AjaxResult.error("更新企业级安全库存失败：" + e.getMessage());
        }
    }

    /**
     * 同步物品总库存
     *
     * @param itemId 物品ID（可选，不传则同步所有物品）
     * @return 操作结果
     */
    @PostMapping("/stock/sync")
    public AjaxResult syncTotalStock(@RequestParam(required = false) String itemId) {
        try {
            if (itemId != null && !itemId.trim().isEmpty()) {
                log.info("同步单个物品总库存，itemId={}", itemId);
                BigDecimal totalStock = itemStockSyncService.syncTotalStock(itemId);
                return AjaxResult.success("同步物品总库存成功，总库存：" + totalStock);
            } else {
                log.info("同步所有物品总库存");
                int successCount = itemStockSyncService.syncAllTotalStock();
                return AjaxResult.success("同步所有物品总库存完成，成功同步：" + successCount + " 个物品");
            }
        } catch (Exception e) {
            log.error("同步物品总库存异常：", e);
            return AjaxResult.error("同步总库存失败：" + e.getMessage());
        }
    }
    
    /**
     * 库存下限告警 - 查询库存不足的物品
     * 查询总库存低于企业级安全库存的物品列表
     *
     * @return 库存不足的物品列表
     */
    @GetMapping("/stock/alert")
    public AjaxResult getLowStockAlert() {
        try {
            log.info("查询库存下限告警");
            List<ItemVo> lowStockItems = itemService.selectLowStockItems();
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("total", lowStockItems.size());
            result.put("items", lowStockItems);
            
            return AjaxResult.success("查询库存告警成功", result);
        } catch (Exception e) {
            log.error("查询库存下限告警异常：", e);
            return AjaxResult.error("查询库存告警失败：" + e.getMessage());
        }
    }

    // ==================== 微信小程序专用接口 ====================

    /**
     * 根据物品条码查询物品信息
     */
    @GetMapping("/by-code/{itemCode}")
    public AjaxResult getItemByCode(@PathVariable String itemCode) {
        try {
            log.info("根据物品条码查询物品信息，条码：{}", itemCode);
            ItemDetailVo item = itemService.getItemByCode(itemCode);
            if (item != null) {
                return AjaxResult.success("查询成功", item);
            } else {
                return AjaxResult.error("未找到对应的物品信息");
            }
        } catch (Exception e) {
            log.error("根据物品条码查询物品信息异常：", e);
            return AjaxResult.error("查询物品信息失败：" + e.getMessage());
        }
    }
}
# RuoYi框架主页路由调整方案

## 问题描述

用户登录成功后出现404错误，无法正常跳转到主页。经分析发现：

1. **登录跳转逻辑**：RuoYi框架登录成功后默认跳转到 `/index` 路径
2. **当前路由配置**：主页配置为 `workbench` 路径，与框架默认不匹配
3. **框架约定**：RuoYi框架的标准设计是登录后跳转到 `/index`

## 解决方案

按照RuoYi框架的标准设计模式，将主页路由调整为 `/index`，保持与框架约定一致。

## 修改内容

### 1. 路由配置调整

**文件**: `device_monitor-ui/src/router/index.js`

**修改前**:
```javascript
{
  path: '',
  component: Layout,
  redirect: 'workbench',
  children: [
    {
      path: 'workbench',
      component: () => import('@/views/workbench/fixed'),
      name: 'Workbench',
      meta: { title: '工作台', icon: 'dashboard', affix: true }
    }
  ]
},
{
  path: '/home',
  component: Layout,
  hidden: true,
  redirect: 'index',
  children: [
    {
      path: 'index',
      component: () => import('@/views/index'),
      name: 'Home',
      meta: { title: '系统首页', icon: 'guide' }
    }
  ]
}
```

**修改后**:
```javascript
{
  path: '',
  component: Layout,
  redirect: 'index',
  children: [
    {
      path: 'index',
      component: () => import('@/views/workbench/fixed'),
      name: 'Index',
      meta: { title: '首页', icon: 'dashboard', affix: true }
    }
  ]
},
{
  path: '/workbench',
  component: Layout,
  hidden: true,
  redirect: 'index',
  children: [
    {
      path: 'index',
      component: () => import('@/views/workbench/fixed'),
      name: 'Workbench',
      meta: { title: '工作台', icon: 'dashboard' }
    }
  ]
}
```

### 2. 页面跳转逻辑调整

**文件**: `device_monitor-ui/src/views/index.vue`

**修改内容**:
- 更新"跳转到数据工作台"的路由地址从 `/workbench` 改为 `/`

## 调整说明

### 主要变更

1. **主页路由**：
   - 路径：从 `workbench` 改为 `index`
   - 组件：仍然使用 `@/views/workbench/fixed`
   - 标题：从 "工作台" 改为 "首页"
   - 重定向：从 `workbench` 改为 `index`

2. **工作台路由**：
   - 保留 `/workbench` 路由作为备用访问路径
   - 设置为隐藏路由 (`hidden: true`)
   - 同样指向工作台组件

3. **兼容性**：
   - 保持了原有的工作台功能不变
   - 用户可以通过 `/` 或 `/index` 访问主页
   - 也可以通过 `/workbench` 访问（隐藏路由）

### 符合RuoYi框架标准

1. **登录跳转**：登录成功后自动跳转到 `/index`
2. **首页概念**：主页使用 `index` 路径，符合框架约定
3. **菜单显示**：首页在侧边栏显示为"首页"，更符合用户习惯
4. **固定标签**：首页标签固定显示 (`affix: true`)

## 预期效果

修改完成后：

1. **登录流程**：
   - 用户登录成功 → 自动跳转到 `/index` → 显示工作台页面
   - 不再出现404错误

2. **用户体验**：
   - 侧边栏显示"首页"菜单项
   - 首页标签固定显示，无法关闭
   - 工作台功能完全保持不变

3. **访问方式**：
   - 主要访问：`/` 或 `/index`
   - 备用访问：`/workbench`（隐藏路由）

## RuoYi框架设计理念

这次调整遵循了RuoYi框架的设计理念：

1. **约定优于配置**：使用框架默认的 `/index` 作为主页路径
2. **用户体验优先**：首页概念更符合用户的使用习惯
3. **框架一致性**：与RuoYi框架的标准设计保持一致
4. **向后兼容**：保留了原有的访问路径作为备用

## 技术细节

### 登录跳转逻辑

RuoYi框架的登录跳转逻辑：
```javascript
// 登录组件中
this.$router.push({ path: this.redirect || "/" })

// 当没有指定redirect时，默认跳转到 "/"
// 路由配置中 path: '' 对应根路径 "/"
// redirect: 'index' 会重定向到 "/index"
```

### 路由匹配规则

```javascript
// 访问 "/" → 匹配 path: '' → redirect: 'index' → 最终访问 "/index"
// 访问 "/index" → 匹配 path: 'index' → 显示工作台组件
// 访问 "/workbench" → 匹配隐藏路由 → 也显示工作台组件
```

## 总结

通过将主页路由调整为符合RuoYi框架标准的 `/index`，解决了登录后404错误的问题，同时保持了工作台功能的完整性。这种调整方式既解决了当前问题，又符合框架的设计规范，是最佳的解决方案。
